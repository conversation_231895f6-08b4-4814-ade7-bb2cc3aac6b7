import { ThemeConfig } from 'antd';

export const lightTheme: ThemeConfig = {
  token: {
    // 主色调
    colorPrimary: '#1a73e8',
    colorSuccess: '#34a853',
    colorWarning: '#fbbc04',
    colorError: '#ea4335',
    colorInfo: '#1a73e8',
    
    // 背景色
    colorBgBase: '#ffffff',
    colorBgContainer: '#ffffff',
    colorBgElevated: '#ffffff',
    colorBgLayout: '#f8f9fa',
    colorBgSpotlight: '#ffffff',
    
    // 文字颜色
    colorText: '#202124',
    colorTextSecondary: '#5f6368',
    colorTextTertiary: '#80868b',
    colorTextQuaternary: '#9aa0a6',
    
    // 边框
    colorBorder: '#dadce0',
    colorBorderSecondary: '#e8eaed',
    
    // 填充色
    colorFill: '#f1f3f4',
    colorFillSecondary: '#f8f9fa',
    colorFillTertiary: '#ffffff',
    colorFillQuaternary: '#ffffff',
    
    // 圆角
    borderRadius: 8,
    borderRadiusLG: 12,
    borderRadiusSM: 6,
    borderRadiusXS: 4,
    
    // 字体
    fontFamily: 'Google Sans, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, sans-serif',
    fontSize: 16,
    fontSizeLG: 18,
    fontSizeSM: 14,
    fontSizeXL: 22,
    
    // 间距
    padding: 16,
    paddingLG: 24,
    paddingSM: 12,
    paddingXS: 8,
    paddingXXS: 4,
    
    // 阴影
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
    boxShadowSecondary: '0 4px 16px rgba(0, 0, 0, 0.08)',
    boxShadowTertiary: '0 8px 32px rgba(0, 0, 0, 0.12)',
    
    // 动画
    motionDurationFast: '0.1s',
    motionDurationMid: '0.2s',
    motionDurationSlow: '0.3s',
    motionEaseInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
    motionEaseOut: 'cubic-bezier(0.0, 0, 0.2, 1)',
    // motionEaseIn: 'cubic-bezier(0.4, 0, 1, 1)', // 移除不支持的属性
  },
  components: {
    Button: {
      borderRadius: 8,
      controlHeight: 40,
      controlHeightSM: 32,
      controlHeightLG: 48,
      fontWeight: 500,
      primaryShadow: '0 2px 4px rgba(26, 115, 232, 0.2)',
    },
    Input: {
      borderRadius: 8,
      controlHeight: 40,
      controlHeightSM: 32,
      controlHeightLG: 48,
      paddingInline: 12,
    },
    Card: {
      borderRadiusLG: 12,
      paddingLG: 24,
      boxShadowTertiary: '0 2px 8px rgba(0, 0, 0, 0.06)',
    },
    Modal: {
      borderRadiusLG: 12,
      paddingLG: 24,
    },
    Drawer: {
      borderRadiusLG: 12,
      paddingLG: 24,
    },
    Menu: {
      borderRadius: 8,
      itemBorderRadius: 6,
      itemHeight: 40,
      itemPaddingInline: 12,
    },
    Tabs: {
      borderRadius: 8,
      itemColor: '#5f6368',
      itemSelectedColor: '#1a73e8',
      itemHoverColor: '#1a73e8',
    },
    Select: {
      borderRadius: 8,
      controlHeight: 40,
      controlHeightSM: 32,
      controlHeightLG: 48,
    },
    Switch: {
      borderRadius: 100,
    },
    Slider: {
      borderRadius: 100,
      handleSize: 20,
      handleSizeHover: 24,
      railSize: 4,
      // trackSize: 4, // 移除不支持的属性
    },
    Progress: {
      borderRadius: 100,
    },
    Avatar: {
      borderRadius: 8,
    },
    Badge: {
      borderRadius: 10,
    },
    Tag: {
      borderRadius: 6,
    },
    Alert: {
      borderRadius: 8,
    },
    Message: {
      borderRadius: 8,
    },
    Notification: {
      borderRadius: 8,
    },
    Tooltip: {
      borderRadius: 6,
    },
    Popover: {
      borderRadius: 8,
    },
    Dropdown: {
      borderRadius: 8,
    },
  },
};

export const darkTheme: ThemeConfig = {
  ...lightTheme,
  token: {
    ...lightTheme.token,
    // 背景色
    colorBgBase: '#1f1f1f',
    colorBgContainer: '#2d2d2d',
    colorBgElevated: '#383838',
    colorBgLayout: '#141414',
    colorBgSpotlight: '#2d2d2d',
    
    // 文字颜色
    colorText: '#ffffff',
    colorTextSecondary: '#b3b3b3',
    colorTextTertiary: '#8c8c8c',
    colorTextQuaternary: '#595959',
    
    // 边框
    colorBorder: '#434343',
    colorBorderSecondary: '#303030',
    
    // 填充色
    colorFill: '#262626',
    colorFillSecondary: '#1f1f1f',
    colorFillTertiary: '#141414',
    colorFillQuaternary: '#0f0f0f',
  },
};

export const themeConfig = {
  light: lightTheme,
  dark: darkTheme,
};

export type ThemeMode = 'light' | 'dark';
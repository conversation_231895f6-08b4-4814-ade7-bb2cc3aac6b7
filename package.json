{"name": "@lobehub/chat", "version": "1.104.4", "description": "Lobe Chat - an open-source, high-performance chatbot framework that supports speech synthesis, multimodal, and extensible Function Call plugin system. Supports one-click free deployment of your private ChatGPT/LLM web application.", "keywords": ["framework", "chatbot", "chatgpt", "nextjs", "vercel-ai", "openai", "azure-openai", "visual-model", "tts", "stt"], "homepage": "https://github.com/lobehub/lobe-chat", "bugs": {"url": "https://github.com/lobehub/lobe-chat/issues/new/choose"}, "repository": {"type": "git", "url": "https://github.com/lobehub/lobe-chat.git"}, "license": "MIT", "author": "LobeHub <<EMAIL>>", "sideEffects": false, "workspaces": ["packages/*"], "scripts": {"prebuild": "tsx scripts/prebuild.mts", "build": "cross-env NODE_OPTIONS=--max-old-space-size=6144 next build", "postbuild": "npm run build-sitemap && npm run build-migrate-db", "build-migrate-db": "bun run db:migrate", "build-sitemap": "tsx ./scripts/buildSitemapIndex/index.ts", "build:analyze": "NODE_OPTIONS=--max-old-space-size=6144 ANALYZE=true next build", "build:docker": "npm run prebuild && DOCKER=true next build && npm run build-sitemap", "prebuild:electron": "cross-env NEXT_PUBLIC_IS_DESKTOP_APP=1 tsx scripts/prebuild.mts", "build:electron": "cross-env NODE_OPTIONS=--max-old-space-size=6144 NEXT_PUBLIC_IS_DESKTOP_APP=1 NEXT_PUBLIC_SERVICE_MODE=server next build", "db:generate": "drizzle-kit generate && npm run db:generate-client && npm run workflow:dbml", "db:generate-client": "tsx ./scripts/migrateClientDB/compile-migrations.ts", "db:migrate": "MIGRATION_DB=1 tsx ./scripts/migrateServerDB/index.ts", "db:studio": "drizzle-kit studio", "db:visualize": "dbdocs build docs/development/database-schema.dbml --project lobe-chat", "desktop:build": "npm run desktop:build-next && npm run desktop:prepare-dist && npm run desktop:build-electron", "desktop:build-electron": "tsx scripts/electronWorkflow/buildElectron.ts", "desktop:build-local": "npm run desktop:build-next && npm run desktop:prepare-dist && npm run build-local --prefix=./apps/desktop", "desktop:build-next": "npm run build:electron", "desktop:prepare-dist": "tsx scripts/electronWorkflow/moveNextStandalone.ts", "dev": "next dev --turbopack -p 3010", "dev:desktop": "next dev --turbopack -p 3015", "docs:i18n": "lobe-i18n md && npm run lint:md && npm run lint:mdx && prettier -c --write locales/**/*", "docs:seo": "lobe-seo && npm run lint:mdx", "i18n": "npm run workflow:i18n && lobe-i18n && prettier -c --write \"locales/**\"", "lint": "npm run lint:ts && npm run lint:style && npm run type-check && npm run lint:circular", "lint:circular": "dpdm src/**/*.ts  --no-warning --no-tree --exit-code circular:1 --no-progress -T true --skip-dynamic-imports circular", "lint:md": "remark . --silent --output", "lint:mdx": "npm run workflow:mdx && remark \"docs/**/*.mdx\" -r ./.remarkrc.mdx.js --silent --output && eslint \"docs/**/*.mdx\" --quiet --fix", "lint:style": "stylelint \"{src,tests}/**/*.{js,jsx,ts,tsx}\" --fix", "lint:ts": "eslint \"{src,tests}/**/*.{js,jsx,ts,tsx}\" --fix", "prepare": "husky", "prettier": "prettier -c --write \"**/**\"", "pull": "git pull", "reinstall": "rm -rf pnpm-lock.yaml && rm -rf node_modules && pnpm -r exec rm -rf node_modules && pnpm install", "reinstall:desktop": "rm -rf pnpm-lock.yaml && rm -rf node_modules && pnpm -r exec rm -rf node_modules && pnpm install --node-linker=hoisted", "release": "semantic-release", "self-hosting:docker": "docker build -t lobe-chat:local .", "self-hosting:docker-cn": "docker build -t lobe-chat-local --build-arg USE_CN_MIRROR=true .", "self-hosting:docker-cn@database": "docker build -t lobe-chat-database-local -f Dockerfile.database --build-arg USE_CN_MIRROR=true .", "start": "next start -p 3210", "stylelint": "stylelint \"src/**/*.{js,jsx,ts,tsx}\" --fix", "test": "npm run test-app && npm run test-server", "test-app": "vitest run --config vitest.config.ts", "test-app:coverage": "vitest run --config vitest.config.ts  --coverage", "test-server": "vitest run --config vitest.config.server.ts", "test-server:coverage": "vitest run --config vitest.config.server.ts --coverage", "test:update": "vitest -u", "type-check": "tsgo --noEmit", "webhook:ngrok": "ngrok http http://localhost:3011", "workflow:cdn": "tsx ./scripts/cdnWorkflow/index.ts", "workflow:changelog": "tsx ./scripts/changelogWorkflow/index.ts", "workflow:countCharters": "tsx scripts/countEnWord.ts", "workflow:dbml": "tsx ./scripts/dbmlWorkflow/index.ts", "workflow:docs": "tsx ./scripts/docsWorkflow/index.ts", "workflow:i18n": "tsx ./scripts/i18nWorkflow/index.ts", "workflow:mdx": "tsx ./scripts/mdxWorkflow/index.ts", "workflow:readme": "tsx ./scripts/readmeWorkflow/index.ts", "workflow:set-desktop-version": "tsx ./scripts/electronWorkflow/setDesktopVersion.ts"}, "lint-staged": {"*.md": ["remark --silent --output --", "prettier --write --no-error-on-unmatched-pattern"], "*.mdx": ["remark -r ./.remarkrc.mdx.js --silent --output --", "eslint --quiet --fix"], "*.json": ["prettier --write --no-error-on-unmatched-pattern"], "*.{mjs,cjs}": ["prettier --write", "eslint --fix"], "*.{js,jsx}": ["prettier --write", "stylelint --fix", "eslint --fix"], "*.{ts,tsx}": ["prettier --parser=typescript --write", "stylelint --fix", "eslint --fix"]}, "overrides": {"mdast-util-gfm-autolink-literal": "2.0.0"}, "dependencies": {"@ant-design/icons": "^5.6.1", "@ant-design/pro-components": "^2.8.10", "@anthropic-ai/sdk": "^0.56.0", "@auth/core": "^0.38.0", "@aws-sdk/client-bedrock-runtime": "^3.848.0", "@aws-sdk/client-s3": "^3.850.0", "@aws-sdk/s3-request-presigner": "^3.850.0", "@azure-rest/ai-inference": "1.0.0-beta.5", "@azure/core-auth": "^1.10.0", "@cfworker/json-schema": "^4.1.1", "@clerk/localizations": "^3.20.1", "@clerk/nextjs": "^6.25.4", "@clerk/themes": "^2.3.3", "@codesandbox/sandpack-react": "^2.20.0", "@cyntler/react-doc-viewer": "^1.17.0", "@electric-sql/pglite": "0.2.17", "@fal-ai/client": "^1.6.1", "@formkit/auto-animate": "^0.8.2", "@google/genai": "^1.10.0", "@huggingface/inference": "^2.8.1", "@icons-pack/react-simple-icons": "9.6.0", "@khmyznikov/pwa-install": "0.3.9", "@langchain/community": "^0.3.49", "@lobechat/electron-client-ipc": "workspace:*", "@lobechat/electron-server-ipc": "workspace:*", "@lobechat/file-loaders": "workspace:*", "@lobechat/web-crawler": "workspace:*", "@lobehub/analytics": "^1.5.1", "@lobehub/charts": "^2.0.0", "@lobehub/chat-plugin-sdk": "^1.32.4", "@lobehub/chat-plugins-gateway": "^1.9.0", "@lobehub/icons": "^2.17.0", "@lobehub/market-sdk": "^0.22.7", "@lobehub/tts": "^2.0.1", "@lobehub/ui": "^2.7.5", "@modelcontextprotocol/sdk": "^1.16.0", "@neondatabase/serverless": "^1.0.1", "@next/third-parties": "^15.4.3", "@react-spring/web": "^9.7.5", "@sentry/nextjs": "^7.120.3", "@serwist/next": "^9.1.1", "@t3-oss/env-nextjs": "^0.12.0", "@tanstack/react-query": "^5.83.0", "@trpc/client": "^11.4.3", "@trpc/next": "^11.4.3", "@trpc/react-query": "^11.4.3", "@trpc/server": "^11.4.3", "@vercel/analytics": "^1.5.0", "@vercel/edge-config": "^1.4.0", "@vercel/functions": "^2.2.4", "@vercel/speed-insights": "^1.2.0", "@xterm/xterm": "^5.5.0", "ahooks": "^3.9.0", "antd": "^5.26.6", "antd-style": "^3.7.1", "brotli-wasm": "^3.0.1", "chroma-js": "^3.1.2", "cookie": "^1.0.2", "countries-and-timezones": "^3.8.0", "dayjs": "^1.11.13", "debug": "^4.4.1", "dexie": "^3.2.7", "diff": "^7.0.0", "drizzle-orm": "^0.41.0", "drizzle-zod": "^0.5.1", "epub2": "^3.0.2", "fast-deep-equal": "^3.1.3", "file-type": "^20.5.0", "framer-motion": "^12.23.6", "gpt-tokenizer": "^2.9.0", "gray-matter": "^4.0.3", "html-to-text": "^9.0.5", "i18next": "^24.2.3", "i18next-browser-languagedetector": "^8.2.0", "i18next-resources-to-backend": "^1.2.1", "idb-keyval": "^6.2.2", "immer": "^10.1.1", "jose": "^5.10.0", "js-sha256": "^0.11.1", "jsonl-parse-stringify": "^1.0.3", "keyv": "^4.5.4", "langchain": "^0.3.30", "langfuse": "^3.38.4", "langfuse-core": "^3.38.4", "lodash-es": "^4.17.21", "lucide-react": "^0.525.0", "mammoth": "^1.9.1", "markdown-to-txt": "^2.0.1", "mdast-util-to-markdown": "^2.1.2", "modern-screenshot": "^4.6.5", "nanoid": "^5.1.5", "next": "~15.3.5", "next-auth": "5.0.0-beta.29", "next-mdx-remote": "^5.0.0", "nextjs-toploader": "^3.8.16", "node-machine-id": "^1.1.12", "numeral": "^2.0.6", "nuqs": "^2.4.3", "officeparser": "5.1.1", "oidc-provider": "^9.4.0", "ollama": "^0.5.16", "openai": "^4.104.0", "openapi-fetch": "^0.9.8", "partial-json": "^0.1.7", "path-browserify-esm": "^1.0.6", "pdf-parse": "^1.1.1", "pdfjs-dist": "4.8.69", "pg": "^8.16.3", "pino": "^9.7.0", "plaiceholder": "^3.0.0", "polished": "^4.3.1", "posthog-js": "^1.257.2", "pure-rand": "^7.0.1", "pwa-install-handler": "^2.6.2", "query-string": "^9.2.2", "random-words": "^2.0.1", "react": "^19.1.0", "react-confetti": "^6.4.0", "react-dom": "^19.1.0", "react-fast-marquee": "^1.6.5", "react-hotkeys-hook": "^5.1.0", "react-i18next": "^15.6.1", "react-layout-kit": "^2.0.0", "react-lazy-load": "^4.0.1", "react-pdf": "^9.2.1", "react-rnd": "^10.5.2", "react-scan": "^0.3.6", "react-virtuoso": "^4.13.0", "react-wrap-balancer": "^1.1.1", "remark": "^15.0.1", "remark-gfm": "^4.0.1", "remark-html": "^16.0.1", "request-filtering-agent": "^2.0.1", "resolve-accept-language": "^3.1.12", "rtl-detect": "^1.1.2", "semver": "^7.7.2", "sharp": "^0.34.3", "shiki": "^3.8.1", "stripe": "^16.12.0", "superjson": "^2.2.2", "svix": "^1.69.0", "swr": "^2.3.4", "systemjs": "^6.15.1", "tokenx": "^0.4.1", "ts-md5": "^1.3.1", "type-fest": "^4.41.0", "ua-parser-js": "^1.0.40", "unstructured-client": "^0.19.0", "url-join": "^5.0.0", "use-merge-value": "^1.2.0", "uuid": "^11.1.0", "ws": "^8.18.3", "y-protocols": "^1.0.6", "y-webrtc": "^10.3.0", "yaml": "^2.8.0", "yjs": "^13.6.27", "zod": "^3.25.76", "zustand": "5.0.4", "zustand-utils": "^2.1.0"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@edge-runtime/vm": "^5.0.0", "@huggingface/tasks": "^0.15.9", "@lobehub/i18n-cli": "^1.25.1", "@lobehub/lint": "^1.26.2", "@lobehub/market-types": "^1.11.4", "@lobehub/seo-cli": "^1.7.0", "@next/bundle-analyzer": "^15.4.3", "@next/eslint-plugin-next": "^15.4.3", "@peculiar/webcrypto": "^1.5.0", "@prettier/sync": "^0.6.1", "@semantic-release/exec": "^6.0.3", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/chroma-js": "^3.1.1", "@types/crypto-js": "^4.2.2", "@types/debug": "^4.1.12", "@types/diff": "^7.0.2", "@types/fs-extra": "^11.0.4", "@types/ip": "^1.1.3", "@types/json-schema": "^7.0.15", "@types/lodash": "^4.17.20", "@types/lodash-es": "^4.17.12", "@types/node": "^22.16.5", "@types/numeral": "^2.0.5", "@types/oidc-provider": "^9.1.1", "@types/pg": "^8.15.4", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/rtl-detect": "^1.0.3", "@types/semver": "^7.7.0", "@types/systemjs": "^6.15.3", "@types/ua-parser-js": "^0.7.39", "@types/unist": "^3.0.3", "@types/uuid": "^10.0.0", "@types/ws": "^8.18.1", "@typescript/native-preview": "7.0.0-dev.20250711.1", "@vitest/coverage-v8": "^3.2.4", "ajv-keywords": "^5.1.0", "commitlint": "^19.8.1", "consola": "^3.4.2", "cross-env": "^7.0.3", "crypto-js": "^4.2.0", "dbdocs": "^0.14.4", "dotenv": "^16.6.1", "dpdm-fast": "1.0.7", "drizzle-dbml-generator": "^0.10.0", "drizzle-kit": "^0.31.4", "eslint": "^8.57.1", "eslint-plugin-mdx": "^3.6.2", "fake-indexeddb": "^6.0.1", "fs-extra": "^11.3.0", "glob": "^11.0.3", "happy-dom": "^17.6.3", "husky": "^9.1.7", "just-diff": "^6.0.2", "lint-staged": "^15.5.2", "lodash": "^4.17.21", "markdown-table": "^3.0.4", "mcp-hello-world": "^1.1.2", "mime": "^4.0.7", "node-fetch": "^3.3.2", "node-gyp": "^11.2.0", "openapi-typescript": "^7.8.0", "p-map": "^7.0.3", "prettier": "^3.6.2", "remark-cli": "^12.0.1", "remark-frontmatter": "^5.0.0", "remark-mdx": "^3.1.0", "remark-parse": "^11.0.0", "semantic-release": "^21.1.2", "serwist": "^9.1.1", "stylelint": "^15.11.0", "tsx": "~4.19.4", "type-fest": "^4.41.0", "typescript": "^5.8.3", "unified": "^11.0.5", "unist-util-visit": "^5.0.0", "vite": "^5.4.19", "vitest": "^3.2.4", "vitest-canvas-mock": "^0.3.3"}, "packageManager": "pnpm@10.10.0", "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org"}, "pnpm": {"onlyBuiltDependencies": ["@vercel/speed-insights"], "overrides": {"mdast-util-gfm-autolink-literal": "2.0.0"}}}
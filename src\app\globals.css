@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Google+Sans:wght@300;400;500;600&display=swap');
@import url('https://fonts.googleapis.com/icon?family=Material+Icons');

:root {
  --foreground-rgb: 32, 33, 36;
  --background-start-rgb: 248, 249, 250;
  --background-end-rgb: 255, 255, 255;
  --primary-color: #1a73e8;
  --primary-hover: #1557b0;
  --secondary-color: #34a853;
  --danger-color: #dc2626;
  --danger-hover-color: #b91c1c;
  --background-color: #f8f9fa;
  --surface-color: #ffffff;
  --text-primary: #202124;
  --text-secondary: #5f6368;
  --border-color: #dadce0;
  --border-radius: 8px;
  --shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
  --shadow-hover: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23);
  --transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: 'Google Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  height: 100vh;
  margin: 0;
  padding: 0;
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
  /* 提高合成层级，强制硬件加速，否则会有渲染黑边出现 */
  will-change: opacity;
  transform: translateZ(0);
}

a {
  color: inherit;
  text-decoration: none;
}

/* 自定义滚动条样式 */
* {
  scrollbar-color: var(--border-color) transparent;
  scrollbar-width: thin;
}

::-webkit-scrollbar {
  width: 0.75em;
  height: 0.75em;
}

::-webkit-scrollbar-thumb {
  border-radius: 10px;
}

:hover::-webkit-scrollbar-thumb {
  border: 3px solid transparent;
  background-color: var(--text-secondary);
  background-clip: content-box;
}

::-webkit-scrollbar-track {
  background-color: transparent;
}

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  body {
    font-size: 16px;
  }
}

/* 禁用选择文本 */
.no-select {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* 加载动画 */
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid rgba(26, 115, 232, 0.3);
  border-radius: 50%;
  border-top-color: #1a73e8;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Ant Design 样式覆盖 */
.ant-btn {
  border-radius: var(--border-radius);
  transition: var(--transition);
}

.ant-input {
  border-radius: var(--border-radius);
}

.ant-card {
  border-radius: 12px;
  box-shadow: var(--shadow);
}

.ant-modal {
  border-radius: 12px;
}

/* 自定义工具类 */
.glass-effect {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.8);
}

.text-gradient {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
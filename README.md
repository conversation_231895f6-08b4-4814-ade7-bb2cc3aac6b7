# 微甜 AI Studio

一个基于 Next.js 的现代化 AI 对话与创作平台，支持智能体管理、提示词管理、用户认证等功能。

## ✨ 功能特性

- 🤖 **智能对话**: 支持多种 AI 模型的智能对话
- 👥 **智能体管理**: 创建和管理自定义 AI 智能体
- 📝 **提示词管理**: 组织和复用提示词模板
- 🔄 **批量生成**: 批量处理和生成内容
- 🔐 **用户认证**: 支持邮箱注册和第三方登录（微信、QQ）
- 📱 **响应式设计**: 适配桌面和移动设备
- 🎨 **现代化UI**: 基于 Ant Design 的美观界面

## 🛠️ 技术栈

- **前端**: Next.js 15, React 18, TypeScript
- **UI组件**: Ant Design, Lobe UI
- **状态管理**: Zustand
- **数据库**: MySQL 8.0
- **认证**: JWT, bcryptjs
- **样式**: Ant Design Style, Tailwind CSS
- **部署**: Docker, Docker Compose

## 🚀 快速开始

### 环境要求

- Node.js >= 18.0.0
- MySQL >= 8.0
- npm >= 8.0.0

### 本地开发

1. **克隆项目**
   ```bash
   git clone https://github.com/your-org/story-ai-studio.git
   cd story-ai-studio
   ```

2. **安装依赖**
   ```bash
   npm install --legacy-peer-deps
   ```

3. **配置环境变量**
   ```bash
   # 复制环境配置文件
   cp .env.local.example .env.local
   # 编辑 .env.local 文件，配置数据库密码等信息
   ```

4. **初始化数据库**
   ```bash
   # 自动创建数据库和表结构
   npm run db:init
   ```

5. **启动开发服务器**
   ```bash
   npm run dev
   ```

6. **访问应用**
   打开浏览器访问 [http://localhost:3000](http://localhost:3000)

### Docker 部署

1. **使用 Docker Compose（推荐）**
   ```bash
   # 复制生产环境配置
   cp .env.production .env

   # 启动所有服务（包括MySQL）
   docker-compose up -d
   ```

2. **单独构建镜像**
   ```bash
   docker build -t story-ai-studio .
   docker run -p 3000:3000 story-ai-studio
   ```

3. **使用部署脚本**
   ```bash
   chmod +x scripts/deploy.sh
   ./scripts/deploy.sh production
   ```

## 📁 项目结构

```
story-ai-studio/
├── src/
│   ├── app/                 # Next.js App Router
│   │   ├── api/            # API 路由
│   │   │   ├── auth/       # 认证相关API
│   │   │   ├── agents/     # 智能体API
│   │   │   ├── prompts/    # 提示词API
│   │   │   └── health/     # 健康检查
│   │   ├── auth/           # 认证页面
│   │   ├── chat/           # 聊天页面
│   │   ├── agents/         # 智能体管理
│   │   ├── prompts/        # 提示词管理
│   │   └── settings/       # 设置页面
│   ├── components/         # 共享组件
│   ├── store/             # Zustand状态管理
│   └── lib/               # 工具函数
├── scripts/               # 脚本文件
│   ├── init-db.sql        # 数据库初始化
│   └── deploy.sh          # 部署脚本
├── public/               # 静态资源
├── docker-compose.yml    # Docker 编排
├── Dockerfile           # Docker 镜像
└── package.json         # 项目配置
```

## 🔧 配置说明

### 环境变量

| 变量名 | 说明 | 默认值 | 必需 |
|--------|------|--------|------|
| `DB_HOST` | 数据库主机 | localhost | ✅ |
| `DB_PORT` | 数据库端口 | 3306 | ✅ |
| `DB_USER` | 数据库用户 | root | ✅ |
| `DB_PASSWORD` | 数据库密码 | password | ✅ |
| `DB_NAME` | 数据库名称 | story_ai | ✅ |
| `JWT_SECRET` | JWT 密钥 | - | ✅ |
| `WECHAT_APP_ID` | 微信应用ID | - | ❌ |
| `QQ_APP_ID` | QQ应用ID | - | ❌ |

### 数据库配置

项目使用 MySQL 8.0，包含以下主要表：

- **users**: 用户信息（邮箱、密码、第三方登录等）
- **agents**: 智能体配置（名称、描述、系统提示词等）
- **prompts**: 提示词模板（标题、内容、分类等）
- **sessions**: 对话会话（消息历史、智能体关联等）

## 📝 开发指南

### 可用脚本

```bash
npm run dev          # 启动开发服务器
npm run build        # 构建生产版本
npm run start        # 启动生产服务器
npm run lint         # 代码检查
npm run lint:fix     # 自动修复代码问题
npm run type-check   # TypeScript类型检查
npm run db:init      # 初始化数据库
npm run db:reset     # 重置数据库
npm run clean        # 清理构建文件
```

### 添加新功能

1. 在 `src/app` 下创建新的页面路由
2. 在 `src/components` 下创建可复用组件
3. 在 `src/store` 下添加状态管理
4. 在 `src/app/api` 下添加 API 接口

### 代码规范

- 使用 TypeScript 进行类型检查
- 遵循 ESLint 代码规范
- 使用 Prettier 格式化代码
- 提交前运行 `npm run lint` 和 `npm run type-check`

## 🚀 部署指南

### 生产环境部署

1. **准备服务器**
   - 安装 Docker 和 Docker Compose
   - 配置域名和 SSL 证书

2. **配置环境**
   ```bash
   # 复制生产环境配置
   cp .env.production .env
   # 修改配置文件中的敏感信息
   ```

3. **部署应用**
   ```bash
   # 使用部署脚本
   ./scripts/deploy.sh production
   ```

### 监控和维护

- 使用 `docker-compose logs -f app` 查看应用日志
- 访问 `/api/health` 检查应用健康状态
- 定期备份数据库数据

## 🔐 认证系统

### 支持的登录方式

1. **邮箱密码登录**
   - 用户注册时需要邮箱验证
   - 支持"记住我"功能（7天 vs 1小时）
   - 密码使用 bcrypt 加密存储

2. **第三方登录**
   - 微信登录（需配置 WECHAT_APP_ID 和 WECHAT_APP_SECRET）
   - QQ登录（需配置 QQ_APP_ID 和 QQ_APP_SECRET）

### 安全特性

- JWT 令牌认证
- 密码强度验证
- 邮箱验证机制
- 防止重复注册

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系我们

- 项目主页: [https://github.com/your-org/story-ai-studio](https://github.com/your-org/story-ai-studio)
- 问题反馈: [Issues](https://github.com/your-org/story-ai-studio/issues)
- 邮箱: <EMAIL>

---

**微甜 AI Studio** - 让创意无限可能 ✨
